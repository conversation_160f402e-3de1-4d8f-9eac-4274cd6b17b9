#include "glog.h"
#include "MoistureAnalyzerDriver.h"
#include <iomanip>
#include <sstream>
#include <cstring>
#include <algorithm>
#include <thread>
#include <chrono>
#include <cmath>
#include <functional>

namespace AnalysisRobot {
namespace Moisture {

MoistureAnalyzerDriver::MoistureAnalyzerDriver()
    : m_serialHandle(INVALID_HANDLE_VALUE)
    , m_status(MoistureStatus::DISCONNECTED)
    , m_statusCallback(nullptr)
    , m_initialWeight(0.0) {
}

MoistureAnalyzerDriver::~MoistureAnalyzerDriver() {
    disconnect();
}

bool MoistureAnalyzerDriver::initialize(const MoistureConfig& config) {
    m_config = config;
    setStatus(MoistureStatus::DISCONNECTED, "Initialized successfully");
    return true;
}

bool MoistureAnalyzerDriver::connect() {
    if (!openSerialPort()) {
        return false;
    }

    if (!configureSerialPort()) {
        closeSerialPort();
        return false;
    }

    setStatus(MoistureStatus::CONNECTED, "Connected successfully");
    return true;
}

void MoistureAnalyzerDriver::disconnect() {
    closeSerialPort();
    setStatus(MoistureStatus::DISCONNECTED, "Disconnected");
}

bool MoistureAnalyzerDriver::isConnected() const {
    return m_status != MoistureStatus::DISCONNECTED && m_status != MoistureStatus::FAULT;
}

WeightReading MoistureAnalyzerDriver::readNetWeight() {
    WeightReading result;

    if (!isConnected()) {
        result.errorMsg = "Not connected to moisture analyzer";
        setError(result.errorMsg);
        return result;
    }

    // 根据文档：立刻读净重 01 03 00 00 00 02
    std::vector<uint16_t> registers;
    if (!executeReadRegisters(0x0000, 2, registers)) {
        result.errorMsg = m_lastError;
        return result;
    }

    // 拼接4字节值
    result.rawValue = (static_cast<uint32_t>(registers[0]) << 16) | registers[1];

    // 检查特殊值
    if (result.rawValue == 0x7FFFFFFF) {
        result.errorMsg = "Overload detected";
        setError(result.errorMsg);
        return result;
    } else if (result.rawValue == 0x80000000) {
        result.errorMsg = "Underload detected";
        setError(result.errorMsg);
        return result;
    }

    // 转换为重量值（假设与天平相同的转换方式）
    result.weight = static_cast<double>(result.rawValue) / 10000.0;
    result.success = true;

    return result;
}

MoistureReading MoistureAnalyzerDriver::readMoisture() {
    MoistureReading result;

    if (!isConnected()) {
        result.errorMsg = "Not connected to moisture analyzer";
        setError(result.errorMsg);
        return result;
    }

    // 根据文档：读水分 01 03 00 05 00 02
    std::vector<uint16_t> registers;
    if (!executeReadRegisters(0x0005, 2, registers)) {
        result.errorMsg = m_lastError;
        return result;
    }

    // 解析水分数据
    // 第一个寄存器可能是水分百分比*100，或者其他格式
    // 第二个寄存器是当前重量的内码值
    uint32_t currentWeightRaw = registers[1];
    result.dryWeight = static_cast<double>(currentWeightRaw) / 10000.0; // 转换为克
    result.initialWeight = m_initialWeight;

    // 打印调试信息
    LOG(INFO) << "Moisture registers: [0]=" << registers[0] << " (0x" << std::hex << registers[0] << std::dec
              << "), [1]=" << registers[1] << " (0x" << std::hex << registers[1] << std::dec << ")";
    LOG(INFO) << "Current weight: " << result.dryWeight << "g, Initial weight: " << m_initialWeight << "g";

    // 计算水分百分比
    if (registers[0] == 0 && m_initialWeight > 0) {
        // 如果第一个寄存器为0，通过重量差计算水分
        double weightLoss = m_initialWeight - result.dryWeight;
        if (weightLoss > 0) {
            result.moisture = (weightLoss / m_initialWeight) * 100.0;
            LOG(INFO) << "Calculated moisture from weight loss: " << weightLoss << "g -> " << result.moisture << "%";
        } else {
            result.moisture = 0.0;
            LOG(WARNING) << "No weight loss detected, moisture = 0%";
        }
    } else if (registers[0] > 0) {
        // 如果第一个寄存器有值，直接使用
        result.moisture = static_cast<double>(registers[0]) / 100.0;
        LOG(INFO) << "Direct moisture from register: " << result.moisture << "%";
    } else {
        result.moisture = 0.0;
        LOG(WARNING) << "Cannot calculate moisture: register[0]=0 and no initial weight set";
    }

    result.success = true;

    return result;
}

MoistureReading MoistureAnalyzerDriver::autoMonitorMoisture(
    int maxUnchangedCount,
    double tolerance,
    int intervalSeconds,
    int maxTimeoutMinutes,
    std::function<void(double, int, int)> progressCallback
) {
    MoistureReading finalResult;

    if (!isConnected()) {
        finalResult.errorMsg = "Not connected to moisture analyzer";
        setError(finalResult.errorMsg);
        return finalResult;
    }

    LOG(INFO) << "Starting auto moisture monitoring - maxUnchangedCount: " << maxUnchangedCount
              << ", tolerance: " << tolerance << "%, interval: " << intervalSeconds
              << "s, timeout: " << maxTimeoutMinutes << "min";

    auto startTime = std::chrono::steady_clock::now();
    double lastMoisturePercent = -1.0;  // 上次的水分百分比
    int unchangedCount = 0;             // 连续不变的次数
    int maxTimeoutSeconds = maxTimeoutMinutes * 60;

    while (true) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - startTime);

        // 检查超时
        if (elapsed.count() > maxTimeoutSeconds) {
            finalResult.errorMsg = "Moisture monitoring timeout after " + std::to_string(maxTimeoutMinutes) + " minutes";
            setError(finalResult.errorMsg);
            LOG(WARNING) << finalResult.errorMsg;
            return finalResult;
        }

        // 读取当前水分
        MoistureReading currentReading = readMoisture();
        if (!currentReading.success) {
            finalResult.errorMsg = "Failed to read moisture: " + currentReading.errorMsg;
            setError(finalResult.errorMsg);
            LOG(ERROR) << finalResult.errorMsg;
            return finalResult;
        }

        double currentMoisturePercent = currentReading.moisture;

        // 检查水分含量是否稳定
        if (lastMoisturePercent >= 0) {
            double moistureDiff = std::abs(currentMoisturePercent - lastMoisturePercent);
            if (moistureDiff <= tolerance) {
                unchangedCount++;
                LOG(INFO) << "Moisture stable - current: " << currentMoisturePercent
                         << "%, diff: " << moistureDiff << "%, count: " << unchangedCount
                         << "/" << maxUnchangedCount;
            } else {
                unchangedCount = 0;  // 重置计数器
                LOG(INFO) << "Moisture changed - current: " << currentMoisturePercent
                         << "%, diff: " << moistureDiff << "%, reset count";
            }
        } else {
            LOG(INFO) << "Initial moisture reading: " << currentMoisturePercent << "%";
        }

        lastMoisturePercent = currentMoisturePercent;

        // 调用进度回调
        if (progressCallback) {
            progressCallback(currentMoisturePercent, unchangedCount, static_cast<int>(elapsed.count()));
        }

        // 检查是否达到稳定条件
        if (unchangedCount >= maxUnchangedCount) {
            LOG(INFO) << "Moisture content stabilized after " << unchangedCount
                     << " consecutive unchanged readings. Final moisture: "
                     << currentMoisturePercent << "%";
            finalResult = currentReading;
            finalResult.success = true;
            return finalResult;
        }

        // 等待下次检测
        std::this_thread::sleep_for(std::chrono::seconds(intervalSeconds));
    }
}

ProcessStatus MoistureAnalyzerDriver::readProcessStatus() {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return ProcessStatus::NORMAL;
    }

    // 根据文档：读过程状态 01 03 00 02 00 01
    std::vector<uint16_t> registers;
    if (!executeReadRegisters(0x0002, 1, registers)) {
        return ProcessStatus::NORMAL;
    }

    return static_cast<ProcessStatus>(registers[0]);
}

bool MoistureAnalyzerDriver::readWeighingStability() {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return false;
    }

    // 根据文档：读称重稳定状态 01 03 00 03 00 01
    std::vector<uint16_t> registers;
    if (!executeReadRegisters(0x0003, 1, registers)) {
        return false;
    }

    return registers[0] == 1;
}

bool MoistureAnalyzerDriver::tare() {
    return executeWriteSingleRegister(0x0004, 0x0002);
}

bool MoistureAnalyzerDriver::internalCalibration() {
    setStatus(MoistureStatus::CALIBRATING, "Starting internal calibration");
    return executeWriteSingleRegister(0x0004, 0x0003);
}

bool MoistureAnalyzerDriver::startHeating() {
    setStatus(MoistureStatus::HEATING, "Starting heating");
    return executeWriteSingleRegister(0x0004, 0x000A);
}

bool MoistureAnalyzerDriver::stopHeating() {
    return executeWriteSingleRegister(0x0004, 0x000B);
}

bool MoistureAnalyzerDriver::returnToWeighing() {
    setStatus(MoistureStatus::CONNECTED, "Returning to weighing mode");
    return executeWriteSingleRegister(0x0004, 0x000C);
}

bool MoistureAnalyzerDriver::openChamber() {
    return executeWriteSingleRegister(0x0004, 0x000D);
}

bool MoistureAnalyzerDriver::closeChamber() {
    return executeWriteSingleRegister(0x0004, 0x000E);
}

bool MoistureAnalyzerDriver::setTargetTemperature(int temperature) {
    return executeWriteSingleRegister(0x0007, temperature);
}

bool MoistureAnalyzerDriver::configureStationAddress(int address) {
    return executeWriteSingleRegister(0x0004, 0x0200 | address);
}

MoistureStatus MoistureAnalyzerDriver::getStatus() const {
    return m_status;
}

void MoistureAnalyzerDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

std::string MoistureAnalyzerDriver::getLastError() const {
    return m_lastError;
}

std::string MoistureAnalyzerDriver::getDeviceInfo() const {
    std::ostringstream oss;
    oss << "Moisture Analyzer Driver\n";
    oss << "Serial Port: " << m_config.serialPort << "\n";
    oss << "Baud Rate: " << m_config.baudRate << "\n";
    oss << "Slave ID: " << m_config.slaveId << "\n";
    oss << "Target Temperature: " << m_config.targetTemperature << "°C\n";
    oss << "Status: ";
    
    switch (m_status) {
        case MoistureStatus::DISCONNECTED: oss << "Disconnected"; break;
        case MoistureStatus::CONNECTED: oss << "Connected"; break;
        case MoistureStatus::CALIBRATING: oss << "Calibrating"; break;
        case MoistureStatus::HEATING: oss << "Heating"; break;
        case MoistureStatus::MEASURING: oss << "Measuring"; break;
        case MoistureStatus::FAULT: oss << "Error"; break;
    }
    
    return oss.str();
}

void MoistureAnalyzerDriver::setInitialWeight(double weight) {
    m_initialWeight = weight;
    LOG(INFO) << "Set initial weight: " << weight << "g";
}

void MoistureAnalyzerDriver::setStatus(MoistureStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void MoistureAnalyzerDriver::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << "MoistureAnalyzerDriver Error: " << error;
}



// CRC16计算函数 - 按照MODBUS标准
uint16_t MoistureAnalyzerDriver::calculateCRC16(const uint8_t* data, size_t length) {
    uint16_t crc = 0xFFFF;

    for (size_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }

    return crc;
}

// 串口操作函数
bool MoistureAnalyzerDriver::openSerialPort() {
    // Windows串口实现
    std::string portName = "\\\\.\\" + m_config.serialPort;
    m_serialHandle = CreateFileA(
        portName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        0,
        NULL
    );

    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        setError("Failed to open serial port: " + m_config.serialPort);
        return false;
    }

    return true;
}

void MoistureAnalyzerDriver::closeSerialPort() {
    if (m_serialHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
    }
}

bool MoistureAnalyzerDriver::configureSerialPort() {
    // Windows串口配置
    DCB dcb = {0};
    dcb.DCBlength = sizeof(dcb);

    if (!GetCommState(m_serialHandle, &dcb)) {
        setError("Failed to get comm state");
        return false;
    }

    dcb.BaudRate = m_config.baudRate;
    dcb.ByteSize = m_config.dataBits;
    dcb.StopBits = (m_config.stopBits == 1) ? ONESTOPBIT : TWOSTOPBITS;
    dcb.Parity = (m_config.parity == 'N') ? NOPARITY :
                 (m_config.parity == 'E') ? EVENPARITY : ODDPARITY;
    dcb.fBinary = TRUE;
    dcb.fParity = (m_config.parity != 'N');

    if (!SetCommState(m_serialHandle, &dcb)) {
        setError("Failed to set comm state");
        return false;
    }

    COMMTIMEOUTS timeouts = {0};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = m_config.responseTimeout;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 50;
    timeouts.WriteTotalTimeoutMultiplier = 10;

    if (!SetCommTimeouts(m_serialHandle, &timeouts)) {
        setError("Failed to set comm timeouts");
        return false;
    }

    return true;
}

bool MoistureAnalyzerDriver::sendModbusCommand(const std::vector<uint8_t>& command) {
    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        setError("Serial port not open");
        return false;
    }

    // 打印发送的指令
    std::ostringstream oss;
    oss << "Sending MODBUS command: ";
    for (size_t i = 0; i < command.size(); i++) {
        oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(command[i]);
        if (i < command.size() - 1) {
            oss << " ";
        }
    }
    LOG(INFO) << oss.str();

    DWORD bytesWritten;
    if (!WriteFile(m_serialHandle, command.data(), command.size(), &bytesWritten, NULL)) {
        setError("Failed to write to serial port");
        return false;
    }

    if (bytesWritten != command.size()) {
        setError("Incomplete write to serial port");
        return false;
    }

    LOG(INFO) << "Successfully sent " << bytesWritten << " bytes";
    return true;
}

int MoistureAnalyzerDriver::receiveModbusResponse(std::vector<uint8_t>& response, size_t expectedLength) {
    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        setError("Serial port not open");
        return -1;
    }

    LOG(INFO) << "Waiting for response, expected length: " << expectedLength;

    response.clear();
    response.resize(expectedLength);

    DWORD bytesRead;
    if (!ReadFile(m_serialHandle, response.data(), expectedLength, &bytesRead, NULL)) {
        DWORD error = GetLastError();
        setError("Failed to read from serial port, error code: " + std::to_string(error));
        return -1;
    }

    response.resize(bytesRead);

    // 打印接收到的响应
    if (bytesRead > 0) {
        std::ostringstream oss;
        oss << "Received response (" << bytesRead << " bytes): ";
        for (DWORD i = 0; i < bytesRead; i++) {
            oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(response[i]);
            if (i < bytesRead - 1) {
                oss << " ";
            }
        }
        LOG(INFO) << oss.str();
    } else {
        LOG(WARNING) << "No data received from serial port";
    }

    return bytesRead;
}

bool MoistureAnalyzerDriver::validateModbusResponse(const std::vector<uint8_t>& response) {
    if (response.size() < 4) {
        setError("Response too short");
        return false;
    }

    // 检查设备地址
    if (response[0] != m_config.slaveId) {
        setError("Invalid slave address in response");
        return false;
    }

    // 检查是否为异常响应
    if (response[1] & 0x80) {
        std::string errorMsg = "MODBUS exception: ";
        switch (response[2]) {
            case 0x01: errorMsg += "Illegal Function"; break;
            case 0x02: errorMsg += "Illegal Data Address"; break;
            case 0x03: errorMsg += "Illegal Data Value"; break;
            case 0x04: errorMsg += "Slave Device Failure"; break;
            case 0x05: errorMsg += "Acknowledge"; break;
            case 0x06: errorMsg += "Slave Device Busy"; break;
            case 0x08: errorMsg += "Memory Parity Error"; break;
            default: errorMsg += "Unknown error code " + std::to_string(response[2]); break;
        }
        setError(errorMsg);
        return false;
    }

    // 验证CRC
    uint16_t receivedCRC = (response[response.size()-1] << 8) | response[response.size()-2];
    uint16_t calculatedCRC = calculateCRC16(response.data(), response.size() - 2);

    if (receivedCRC != calculatedCRC) {
        setError("CRC mismatch");
        return false;
    }

    return true;
}

bool MoistureAnalyzerDriver::executeReadRegisters(uint16_t startAddress, uint16_t registerCount, std::vector<uint16_t>& values) {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return false;
    }

    // 构建MODBUS读寄存器命令
    std::vector<uint8_t> command(8);
    command[0] = m_config.slaveId;      // 设备地址
    command[1] = 0x03;                  // 功能码：读保持寄存器
    command[2] = (startAddress >> 8) & 0xFF;    // 起始地址高字节
    command[3] = startAddress & 0xFF;           // 起始地址低字节
    command[4] = (registerCount >> 8) & 0xFF;   // 寄存器数量高字节
    command[5] = registerCount & 0xFF;          // 寄存器数量低字节

    // 计算并添加CRC
    uint16_t crc = calculateCRC16(command.data(), 6);
    command[6] = crc & 0xFF;            // CRC低字节
    command[7] = (crc >> 8) & 0xFF;     // CRC高字节

    // 发送命令
    if (!sendModbusCommand(command)) {
        return false;
    }

    // 接收响应
    size_t expectedResponseLength = 5 + registerCount * 2; // 地址+功能码+字节数+数据+CRC
    std::vector<uint8_t> response;
    int bytesReceived = receiveModbusResponse(response, expectedResponseLength);

    if (bytesReceived <= 0) {
        setError("No response received");
        return false;
    }

    // 验证响应
    if (!validateModbusResponse(response)) {
        return false;
    }

    // 解析数据
    if (response.size() < 5 + registerCount * 2) {
        setError("Response data incomplete");
        return false;
    }

    values.clear();
    for (uint16_t i = 0; i < registerCount; i++) {
        uint16_t value = (response[3 + i * 2] << 8) | response[4 + i * 2];
        values.push_back(value);
    }

    return true;
}

bool MoistureAnalyzerDriver::executeWriteSingleRegister(uint16_t address, uint16_t value) {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return false;
    }

    // 构建MODBUS写单个寄存器命令
    std::vector<uint8_t> command(8);
    command[0] = m_config.slaveId;      // 设备地址
    command[1] = 0x06;                  // 功能码：写单个寄存器
    command[2] = (address >> 8) & 0xFF; // 寄存器地址高字节
    command[3] = address & 0xFF;        // 寄存器地址低字节
    command[4] = (value >> 8) & 0xFF;   // 数据高字节
    command[5] = value & 0xFF;          // 数据低字节

    // 计算并添加CRC
    uint16_t crc = calculateCRC16(command.data(), 6);
    command[6] = crc & 0xFF;            // CRC低字节
    command[7] = (crc >> 8) & 0xFF;     // CRC高字节

    // 发送命令
    if (!sendModbusCommand(command)) {
        setStatus(MoistureStatus::FAULT, m_lastError);
        return false;
    }

    // 接收响应（写命令的响应应该是原命令的回显）
    std::vector<uint8_t> response;
    int bytesReceived = receiveModbusResponse(response, 8);

    if (bytesReceived <= 0) {
        setError("No response received");
        setStatus(MoistureStatus::FAULT, m_lastError);
        return false;
    }

    // 验证响应
    if (!validateModbusResponse(response)) {
        setStatus(MoistureStatus::FAULT, m_lastError);
        return false;
    }

    // 对于写命令，响应应该是原命令的回显
    if (response.size() != 8 ||
        response[0] != command[0] || response[1] != command[1] ||
        response[2] != command[2] || response[3] != command[3] ||
        response[4] != command[4] || response[5] != command[5]) {
        setError("Invalid write response");
        setStatus(MoistureStatus::FAULT, m_lastError);
        return false;
    }

    return true;
}

} // namespace Moisture
} // namespace AnalysisRobot
