﻿#ifndef MOISTURE_ANALYZER_DRIVER_H
#define MOISTURE_ANALYZER_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <vector>
#include <cstdint>
#include <windows.h>


namespace AnalysisRobot {
namespace Moisture {

/**
 * @brief 水分测定仪状态枚举
 */
enum class MoistureStatus {
    DISCONNECTED = 0,       // 未连接
    CONNECTED = 1,          // 已连接
    CALIBRATING = 2,        // 校准中
    HEATING = 3,            // 加热中
    MEASURING = 4,          // 测量中
    FAULT = 5               // 错误状态
};

/**
 * @brief 过程状态枚举（对应文档中的状态号）
 */
enum class ProcessStatus {
    NORMAL = 1,             // 正常
    CALIBRATING = 2,        // 内校校准中
    CALIBRATION_FAULT = 3,  // 内校故障
    CALIBRATION_COMPLETE = 4, // 内校校准完毕
    CALIBRATION_INVALID = 5,  // 本次内校无效
    CLEAR_PAN = 6,          // 请清盘
    REMOVING_WEIGHT = 7     // 取下砝码中(清盘中)
};

/**
 * @brief 重量读数结果
 */
struct WeightReading {
    bool success;           // 读取是否成功
    double weight;          // 重量值（克）
    uint32_t rawValue;      // 原始值
    std::string errorMsg;   // 错误信息
    
    WeightReading() : success(false), weight(0.0), rawValue(0) {}
};

/**
 * @brief 水分测定结果
 */
struct MoistureReading {
    bool success;           // 测定是否成功
    double moisture;        // 水分百分比
    double dryWeight;       // 干燥后重量（克）
    double initialWeight;   // 初始重量（克）
    std::string errorMsg;   // 错误信息
    
    MoistureReading() : success(false), moisture(0.0), dryWeight(0.0), initialWeight(0.0) {}
};

/**
 * @brief 水分测定仪配置
 */
struct MoistureConfig {
    std::string serialPort;     // 串口号
    int baudRate;               // 波特率，默认9600
    char parity;                // 校验位，默认'N'
    int dataBits;               // 数据位，默认8
    int stopBits;               // 停止位，默认1
    int slaveId;                // 从机地址，默认1
    int responseTimeout;        // 响应超时时间（毫秒）
    int targetTemperature;      // 目标温度（℃）
    
    MoistureConfig() 
        : serialPort("COM1")
        , baudRate(9600)
        , parity('N')
        , dataBits(8)
        , stopBits(1)
        , slaveId(1)
        , responseTimeout(1000)
        , targetTemperature(105) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(MoistureStatus status, const std::string& message)>;

/**
 * @brief 水分测定仪驱动类
 * 
 * 支持MODBUS协议的水分测定仪设备，主要功能：
 * - 读取净重和水分
 * - 去皮和内校操作
 * - 加热控制
 * - 开关仓控制
 * - 状态监控
 */
class MoistureAnalyzerDriver {
public:
    /**
     * @brief 构造函数
     */
    MoistureAnalyzerDriver();
    
    /**
     * @brief 析构函数
     */
    ~MoistureAnalyzerDriver();
    
    /**
     * @brief 初始化连接
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const MoistureConfig& config);
    
    /**
     * @brief 连接设备
     * @return 是否成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    /**
     * @brief 读取净重
     * @return 重量读数结果
     */
    WeightReading readNetWeight();
    
    /**
     * @brief 读取水分
     * @return 水分读数结果
     */
    MoistureReading readMoisture();

    /**
     * @brief 自动监控水分测定过程
     * @param maxUnchangedCount 连续不变的最大次数，默认30次
     * @param tolerance 水分变化容差，默认0.01%
     * @param intervalSeconds 检测间隔秒数，默认5秒
     * @param maxTimeoutMinutes 最大超时时间（分钟），默认10分钟
     * @param progressCallback 进度回调函数，参数为(当前水分%, 稳定计数, 已用时间秒)
     * @return 最终的水分读数结果
     */
    MoistureReading autoMonitorMoisture(
        int maxUnchangedCount = 30,
        double tolerance = 0.01,
        int intervalSeconds = 5,
        int maxTimeoutMinutes = 10,
        std::function<void(double, int, int)> progressCallback = nullptr
    );
    
    /**
     * @brief 读取过程状态
     * @return 过程状态
     */
    ProcessStatus readProcessStatus();
    
    /**
     * @brief 读取称重稳定状态
     * @return 是否稳定
     */
    bool readWeighingStability();
    
    /**
     * @brief 立即去皮
     * @return 是否成功
     */
    bool tare();
    
    /**
     * @brief 内校
     * @return 是否成功
     */
    bool internalCalibration();
    
    /**
     * @brief 开始加热
     * @return 是否成功
     */
    bool startHeating();
    
    /**
     * @brief 停止加热
     * @return 是否成功
     */
    bool stopHeating();
    
    /**
     * @brief 返回称重模式
     * @return 是否成功
     */
    bool returnToWeighing();
    
    /**
     * @brief 开仓
     * @return 是否成功
     */
    bool openChamber();
    
    /**
     * @brief 关仓
     * @return 是否成功
     */
    bool closeChamber();
    
    /**
     * @brief 设置目标温度
     * @param temperature 目标温度（℃）
     * @return 是否成功
     */
    bool setTargetTemperature(int temperature);
    
    /**
     * @brief 配置站地址
     * @param address 新的站地址
     * @return 是否成功
     */
    bool configureStationAddress(int address);
    
    /**
     * @brief 获取当前状态
     * @return 当前状态
     */
    MoistureStatus getStatus() const;
    
    /**
     * @brief 设置状态回调函数
     * @param callback 回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息字符串
     */
    std::string getDeviceInfo() const;

    /**
     * @brief 设置初始重量（用于水分计算）
     * @param weight 初始重量（克）
     */
    void setInitialWeight(double weight);

private:
    HANDLE m_serialHandle;              // Windows串口句柄
    MoistureConfig m_config;            // 配置参数
    MoistureStatus m_status;            // 当前状态
    StatusCallback m_statusCallback;    // 状态回调
    std::string m_lastError;            // 最后错误信息
    double m_initialWeight;             // 初始重量（用于计算水分）
    
    /**
     * @brief 设置状态
     * @param status 新状态
     * @param message 状态消息
     */
    void setStatus(MoistureStatus status, const std::string& message = "");
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
    
    /**
     * @brief 计算CRC16校验码
     * @param data 数据缓冲区
     * @param length 数据长度
     * @return CRC16校验码
     */
    uint16_t calculateCRC16(const uint8_t* data, size_t length);

    /**
     * @brief 发送MODBUS命令
     * @param command 命令数据
     * @return 是否成功
     */
    bool sendModbusCommand(const std::vector<uint8_t>& command);

    /**
     * @brief 接收MODBUS响应
     * @param response 响应数据缓冲区
     * @param expectedLength 期望的响应长度
     * @return 实际接收的长度，-1表示失败
     */
    int receiveModbusResponse(std::vector<uint8_t>& response, size_t expectedLength);

    /**
     * @brief 验证MODBUS响应
     * @param response 响应数据
     * @return 是否有效
     */
    bool validateModbusResponse(const std::vector<uint8_t>& response);

    /**
     * @brief 执行读寄存器命令
     * @param startAddress 起始地址
     * @param registerCount 寄存器数量
     * @param values 读取的值
     * @return 是否成功
     */
    bool executeReadRegisters(uint16_t startAddress, uint16_t registerCount, std::vector<uint16_t>& values);

    /**
     * @brief 执行写单个寄存器命令
     * @param address 寄存器地址
     * @param value 写入值
     * @return 是否成功
     */
    bool executeWriteSingleRegister(uint16_t address, uint16_t value);

    /**
     * @brief 打开串口
     * @return 是否成功
     */
    bool openSerialPort();

    /**
     * @brief 关闭串口
     */
    void closeSerialPort();

    /**
     * @brief 配置串口参数
     * @return 是否成功
     */
    bool configureSerialPort();
};

} // namespace Moisture
} // namespace AnalysisRobot

#endif // MOISTURE_ANALYZER_DRIVER_H
