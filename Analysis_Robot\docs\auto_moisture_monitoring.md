# 自动监控水分测定功能

## 概述

本功能为水分测定仪驱动程序添加了自动监控能力，当连续30次（可配置）检测结果维持不变时，自动结束水分含量测定。这样可以避免过度加热，提高测定效率，并确保结果的准确性。

## 功能特点

- **自动结束条件**: 连续N次检测结果变化小于设定容差时自动结束
- **可配置参数**: 支持自定义连续次数、容差、检测间隔等参数
- **进度监控**: 提供实时进度回调，可监控测定过程
- **超时保护**: 设置最大测定时间，防止无限等待
- **错误处理**: 完善的错误处理和状态反馈

## API 接口

### MoistureAnalyzerDriver::autoMonitorMoisture

```cpp
MoistureReading autoMonitorMoisture(
    int maxUnchangedCount = 30,                              // 连续不变的最大次数
    double tolerance = 0.01,                                 // 水分变化容差 (%)
    int intervalSeconds = 5,                                 // 检测间隔 (秒)
    int maxTimeoutMinutes = 10,                              // 最大超时时间 (分钟)
    std::function<void(double, int, int)> progressCallback = nullptr  // 进度回调函数
);
```

#### 参数说明

- `maxUnchangedCount`: 连续不变的最大次数，默认30次
- `tolerance`: 水分变化容差，默认0.01%（即变化小于0.01%视为不变）
- `intervalSeconds`: 检测间隔秒数，默认5秒
- `maxTimeoutMinutes`: 最大超时时间（分钟），默认10分钟
- `progressCallback`: 进度回调函数，参数为 (当前水分%, 稳定计数, 已用时间秒)

#### 返回值

返回 `MoistureReading` 结构体，包含：
- `success`: 是否成功
- `moisture`: 最终水分百分比
- `dryWeight`: 干燥后重量
- `initialWeight`: 初始重量
- `errorMsg`: 错误信息（如果失败）

## 使用示例

### 基本用法

```cpp
#include "MoistureAnalyzerDriver.h"

using namespace AnalysisRobot::Moisture;

// 创建驱动实例
MoistureAnalyzerDriver moistureAnalyzer;

// 初始化和连接设备
MoistureConfig config;
config.serialPort = "COM9";
moistureAnalyzer.initialize(config);
moistureAnalyzer.connect();

// 执行测定流程
moistureAnalyzer.tare();                    // 去皮
// ... 放入样品 ...
WeightReading weight = moistureAnalyzer.readNetWeight();
moistureAnalyzer.setInitialWeight(weight.weight);
moistureAnalyzer.startHeating();           // 开始加热

// 自动监控（使用默认参数）
MoistureReading result = moistureAnalyzer.autoMonitorMoisture();

if (result.success) {
    std::cout << "水分含量: " << result.moisture << "%" << std::endl;
} else {
    std::cout << "测定失败: " << result.errorMsg << std::endl;
}

moistureAnalyzer.stopHeating();            // 停止加热
```

### 自定义参数

```cpp
// 自定义监控参数
MoistureReading result = moistureAnalyzer.autoMonitorMoisture(
    20,     // 连续20次不变即结束
    0.02,   // 容差0.02%
    3,      // 每3秒检测一次
    15      // 最大15分钟超时
);
```

### 带进度监控

```cpp
// 定义进度回调函数
auto progressCallback = [](double moisture, int stableCount, int elapsedSeconds) {
    std::cout << "[" << elapsedSeconds << "s] "
              << "水分: " << moisture << "%, "
              << "稳定计数: " << stableCount << "/30" << std::endl;
};

// 执行自动监控
MoistureReading result = moistureAnalyzer.autoMonitorMoisture(
    30, 0.01, 5, 10, progressCallback
);
```

## REST API 接口

### 自动监控操作

**请求**:
```json
{
    "action": "AUTO_MONITOR"
}
```

**响应**:
```json
{
    "taskId": 123,
    "status": "SUCCESS",
    "data": {
        "weight": "2.3456g",
        "mc": "12.34%",
        "initial_weight": "2.6789g",
        "monitoring_completed": true,
        "auto_stopped": true
    }
}
```

## 工作原理

1. **初始化**: 设置监控参数和回调函数
2. **循环检测**: 按设定间隔读取水分含量
3. **稳定性判断**: 比较当前读数与上次读数的差值
4. **计数管理**: 
   - 如果差值小于容差，稳定计数+1
   - 如果差值大于容差，稳定计数重置为0
5. **结束条件**: 
   - 稳定计数达到设定值：正常结束
   - 超过最大时间：超时结束
   - 读取失败：错误结束

## 注意事项

1. **设备连接**: 使用前确保设备已正确连接
2. **初始重量**: 必须先设置初始重量才能正确计算水分
3. **加热状态**: 建议在加热过程中使用此功能
4. **参数选择**: 
   - 容差过小可能导致永远无法稳定
   - 容差过大可能导致过早结束
   - 检测间隔过短可能增加设备负担
5. **超时设置**: 建议根据样品特性设置合理的超时时间

## 测试

运行测试程序：
```bash
cd Analysis_Robot/test/moistureAnalyzerDriverTest
./moistureAnalyzerDriverTest.exe
```

选择菜单项 "15. 自动监控水分测试流程" 来测试新功能。

## 示例程序

参考 `Analysis_Robot/examples/auto_moisture_monitoring_example.cpp` 获取完整的使用示例。
