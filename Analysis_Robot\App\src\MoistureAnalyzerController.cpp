#include "MoistureAnalyzerController.h"
#include "MoistureAnalyzerDriver.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <nlohmann/json.hpp>
#include "glog.h"

MoistureAnalyzerController::MoistureAnalyzerController(
    std::shared_ptr<AnalysisRobot::Moisture::MoistureAnalyzerDriver> driver)
    : m_driver(driver)
    , m_nextTaskId(3000) {
    LOG(INFO) << "Moisture analyzer controller created";
}

MoistureAnalyzerController::~MoistureAnalyzerController() {
    LOG(INFO) << "Moisture analyzer controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus MoistureAnalyzerController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Moisture Analyzer";
    status.description = "Intelligent moisture analyzer";
    status.updateTime = getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "FAILED";
        status.message = "Driver not initialized";
        return status;
    }
    
    if (!m_driver->isConnected()) {
        status.status = "FAILED";
        status.message = "Device not connected";
        return status;
    }

    status.status = "SUCCESS";
    status.message = "Device status normal";
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "ZERO") {
        return handleZeroOperation();
    } else if (action == "BARE") {
        return handleBareOperation();
    } else if (action == "OPEN") {
        return handleOpenOperation();
    } else if (action == "CLOSE") {
        return handleCloseOperation();
    } else if (action == "HEAT") {
        nlohmann::json data = request.contains("data") ? request["data"] : nlohmann::json();
        return handleHeatOperation(data);
    } else if (action == "GET_RESULT") {
        return handleGetResultOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleZeroOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("ZERO", "Device not connected");
    }
    
    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("ZERO");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Zero operation completed";
            return task;
        } else {
            return createErrorTask("ZERO", "Zero operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("ZERO", "Zero exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleBareOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("BARE", "Device not connected");
    }
    
    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("BARE");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Bare operation completed";
            return task;
        } else {
            return createErrorTask("BARE", "Tare operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("BARE", "Tare exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleOpenOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("OPEN", "Device not connected");
    }
    
    try {
        if (m_driver->openChamber()) {
            auto task = createSuccessTask("OPEN");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Open operation completed";
            return task;
        } else {
            return createErrorTask("OPEN", "Open cover operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("OPEN", "Open cover exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleCloseOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("CLOSE", "Device not connected");
    }
    
    try {
        if (m_driver->closeChamber()) {
            auto task = createSuccessTask("CLOSE");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Close operation completed";
            return task;
        } else {
            return createErrorTask("CLOSE", "Close cover operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("CLOSE", "Close cover exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleHeatOperation(const nlohmann::json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("HEAT", "Device not connected");
    }

    try {
        // 解析温度参数，默认230°C
        int targetTemperature = 230;
        if (data.contains("temperature") && data["temperature"].is_number()) {
            targetTemperature = data["temperature"].get<int>();
        }

        // 解析时间参数，默认30分钟
        int maxTimeoutMinutes = 30;
        if (data.contains("timeout") && data["timeout"].is_number()) {
            maxTimeoutMinutes = data["timeout"].get<int>();
        }

        LOG(INFO) << "Heat operation parameters - Temperature: " << targetTemperature
                  << "°C, Max time: " << maxTimeoutMinutes << " minutes";

        // 1. 设置目标温度
        if (!m_driver->setTargetTemperature(targetTemperature)) {
            return createErrorTask("HEAT", "Failed to set target temperature: " + m_driver->getLastError());
        }
        LOG(INFO) << "Target temperature set to " << targetTemperature << "°C";

        // 2. 读取当前重量并设置为初始重量
        auto weightReading = m_driver->readNetWeight();
        if (!weightReading.success) {
            return createErrorTask("HEAT", "Failed to read initial weight: " + weightReading.errorMsg);
        }
        m_driver->setInitialWeight(weightReading.weight);
        LOG(INFO) << "Initial weight set to: " << weightReading.weight << "g";

        // 3. 开始加热
        if (!m_driver->startHeating()) {
            return createErrorTask("HEAT", "Failed to start heating: " + m_driver->getLastError());
        }
        LOG(INFO) << "Heating started";

        // 4. 等待设备稳定
        std::this_thread::sleep_for(std::chrono::seconds(10));

        // 5. 执行自动监控（30次不变，容差0.01%，间隔2秒，使用解析的时间参数）
        auto progressCallback = [this](double moisture, int stableCount, int elapsedSeconds) {
            LOG(INFO) << "Auto monitor progress - Time: " << elapsedSeconds
                     << "s, Moisture: " << moisture
                     << "%, Stable count: " << stableCount << "/30";
        };

        auto result = m_driver->autoMonitorMoisture(30, 0.01, 2, maxTimeoutMinutes, progressCallback);

        // 6. 停止加热
        if (!m_driver->stopHeating()) {
            LOG(WARNING) << "Failed to stop heating: " << m_driver->getLastError();
        } else {
            LOG(INFO) << "Heating stopped";
        }

        // 7. 存储结果
        {
            std::lock_guard<std::mutex> lock(m_resultMutex);
            m_lastMoistureResult = result;
        }

        if (result.success) {
            nlohmann::json responseData;
            responseData["status"] = "completed";
            responseData["initial_weight"] = std::to_string(result.initialWeight) + "g";
            responseData["final_weight"] = std::to_string(result.dryWeight) + "g";
            responseData["moisture"] = std::to_string(result.moisture) + "%";

            auto task = createSuccessTask("HEAT", responseData);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Heating and moisture monitoring completed successfully - Moisture: "
                     << result.moisture << "%";
            return task;
        } else {
            return createErrorTask("HEAT", "Auto monitoring failed: " + result.errorMsg);
        }

    } catch (const std::exception& e) {
        return createErrorTask("HEAT", "Heating exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleGetResultOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("GET_RESULT", "Device not connected");
    }

    try {
        // 获取存储的自动监控结果
        AnalysisRobot::Moisture::MoistureReading result;
        {
            std::lock_guard<std::mutex> lock(m_resultMutex);
            result = m_lastMoistureResult;
        }

        if (result.success) {
            nlohmann::json responseData;
            responseData["weight"] = std::to_string(result.dryWeight);
            responseData["mc"] = std::to_string(result.moisture) ;
            responseData["initial_weight"] = std::to_string(result.initialWeight) ;

            auto task = createSuccessTask("GET_RESULT", responseData);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Moisture result - Weight: " << result.dryWeight << "g, MC: " << result.moisture << "%";
            return task;
        } else {
            return createErrorTask("GET_RESULT", "No valid moisture result available or measurement failed");
        }

    } catch (const std::exception& e) {
        return createErrorTask("GET_RESULT", "Get result exception: " + std::string(e.what()));
    }
}

int MoistureAnalyzerController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Command executed successfully";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string MoistureAnalyzerController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


