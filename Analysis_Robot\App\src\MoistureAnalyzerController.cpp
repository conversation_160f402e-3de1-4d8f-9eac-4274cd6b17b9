﻿#include "MoistureAnalyzerController.h"
#include "MoistureAnalyzerDriver.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <nlohmann/json.hpp>
#include "glog.h"

MoistureAnalyzerController::MoistureAnalyzerController(
    std::shared_ptr<AnalysisRobot::Moisture::MoistureAnalyzerDriver> driver)
    : m_driver(driver)
    , m_nextTaskId(3000) {
    LOG(INFO) << "Moisture analyzer controller created";
}

MoistureAnalyzerController::~MoistureAnalyzerController() {
    LOG(INFO) << "Moisture analyzer controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus MoistureAnalyzerController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Moisture Analyzer";
    status.description = "Intelligent moisture analyzer";
    status.updateTime = getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "FAILED";
        status.message = "Driver not initialized";
        return status;
    }
    
    if (!m_driver->isConnected()) {
        status.status = "FAILED";
        status.message = "Device not connected";
        return status;
    }

    status.status = "SUCCESS";
    status.message = "Device status normal";
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "ZERO") {
        return handleZeroOperation();
    } else if (action == "BARE") {
        return handleBareOperation();
    } else if (action == "OPEN") {
        return handleOpenOperation();
    } else if (action == "CLOSE") {
        return handleCloseOperation();
    } else if (action == "HEAT") {
        return handleHeatOperation();
    } else if (action == "WEIGHT") {
        return handleWeightOperation();
    } else if (action == "GET_RESULT") {
        return handleGetResultOperation();
    } else if (action == "AUTO_MONITOR") {
        return handleAutoMonitorOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleZeroOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("ZERO", "Device not connected");
    }
    
    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("ZERO");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Zero operation completed";
            return task;
        } else {
            return createErrorTask("ZERO", "Zero operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("ZERO", "Zero exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleBareOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("BARE", "Device not connected");
    }
    
    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("BARE");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Bare operation completed";
            return task;
        } else {
            return createErrorTask("BARE", "Tare operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("BARE", "Tare exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleOpenOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("OPEN", "Device not connected");
    }
    
    try {
        if (m_driver->openChamber()) {
            auto task = createSuccessTask("OPEN");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Open operation completed";
            return task;
        } else {
            return createErrorTask("OPEN", "Open cover operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("OPEN", "Open cover exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleCloseOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("CLOSE", "Device not connected");
    }
    
    try {
        if (m_driver->closeChamber()) {
            auto task = createSuccessTask("CLOSE");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Close operation completed";
            return task;
        } else {
            return createErrorTask("CLOSE", "Close cover operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("CLOSE", "Close cover exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleHeatOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("HEAT", "Device not connected");
    }
    
    try {
        if (m_driver->startHeating()) {
            auto task = createSuccessTask("HEAT");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Heat operation completed";
            return task;
        } else {
            return createErrorTask("HEAT", "Heating operation failed: " + m_driver->getLastError());
        }

    } catch (const std::exception& e) {
        return createErrorTask("HEAT", "Heating exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleWeightOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("WEIGHT", "Device not connected");
    }
    
    try {
        auto reading = m_driver->readNetWeight();
        
        if (reading.success) {
            nlohmann::json responseData;
            responseData["weight"] = std::to_string(reading.weight) + "g";
            
            auto task = createSuccessTask("WEIGHT", responseData);
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Weight reading: " << reading.weight << "g";
            return task;
        } else {
            return createErrorTask("WEIGHT", "Weighing failed: " + reading.errorMsg);
        }

    } catch (const std::exception& e) {
        return createErrorTask("WEIGHT", "Weighing exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleGetResultOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("GET_RESULT", "Device not connected");
    }
    
    try {
        auto result = m_driver->readMoisture();
        
        if (result.success) {
            nlohmann::json responseData;
            responseData["weight"] = std::to_string(result.dryWeight) + "g";
            responseData["mc"] = std::to_string(result.moisture) + "%";
            
            auto task = createSuccessTask("GET_RESULT", responseData);
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Moisture result - Weight: " << result.dryWeight << "g, MC: " << result.moisture << "%";
            return task;
        } else {
            return createErrorTask("GET_RESULT", "Get result failed: " + result.errorMsg);
        }

    } catch (const std::exception& e) {
        return createErrorTask("GET_RESULT", "Get result exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::handleAutoMonitorOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("AUTO_MONITOR", "Device not connected");
    }

    try {
        // 创建进度回调函数
        auto progressCallback = [this](double moisture, int stableCount, int elapsedSeconds) {
            LOG(INFO) << "Auto monitor progress - Moisture: " << moisture
                     << "%, Stable count: " << stableCount
                     << ", Elapsed: " << elapsedSeconds << "s";
        };

        // 调用自动监控方法
        auto result = m_driver->autoMonitorMoisture(30, 0.01, 5, 10, progressCallback);

        if (result.success) {
            nlohmann::json responseData;
            responseData["weight"] = std::to_string(result.dryWeight) + "g";
            responseData["mc"] = std::to_string(result.moisture) + "%";
            responseData["initial_weight"] = std::to_string(result.initialWeight) + "g";
            responseData["monitoring_completed"] = true;
            responseData["auto_stopped"] = true;

            auto task = createSuccessTask("AUTO_MONITOR", responseData);

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Auto moisture monitoring completed - Weight: " << result.dryWeight
                     << "g, MC: " << result.moisture << "%";
            return task;
        } else {
            return createErrorTask("AUTO_MONITOR", "Auto monitoring failed: " + result.errorMsg);
        }

    } catch (const std::exception& e) {
        return createErrorTask("AUTO_MONITOR", "Auto monitoring exception: " + std::string(e.what()));
    }
}

int MoistureAnalyzerController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Command executed successfully";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo MoistureAnalyzerController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string MoistureAnalyzerController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


