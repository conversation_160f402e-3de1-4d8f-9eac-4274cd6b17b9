#include "HeatingMagneticStirrerController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <nlohmann/json.hpp>
#include "glog.h"

using json = nlohmann::json;

HeatingMagneticStirrerController::HeatingMagneticStirrerController(
    std::shared_ptr<AnalysisRobot::HeatingMagneticStirrer::HeatingMagneticStirrerDriver> driver,
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_driver(driver)
    , m_plcDriver(plcDriver)
    , m_nextTaskId(2000) {
    LOG(INFO) << "Heating magnetic stirrer controller created";
}

HeatingMagneticStirrerController::~HeatingMagneticStirrerController() {
    LOG(INFO) << "Heating magnetic stirrer controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus HeatingMagneticStirrerController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Heating Magnetic Stirrer";
    status.description = "Intelligent heating magnetic stirrer";
    status.updateTime = getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "ERROR";
        status.message = "Driver not initialized";
        return status;
    }
    
    if (!m_driver->isConnected()) {
        status.status = "DISCONNECTED";
        status.message = "Device not connected";
        return status;
    }
    
    auto driverStatus = m_driver->getStatus();
    switch (driverStatus) {
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::CONNECTED:
            status.status = "IDLE";
            status.message = "Device idle and available";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::HEATING:
            status.status = "HEATING";
            status.message = "Heating in progress";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::STIRRING:
            status.status = "STIRRING";
            status.message = "Stirring in progress";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::HEATING_STIRRING:
            status.status = "WORKING";
            status.message = "Heating and stirring in progress";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::FAULT:
            status.status = "FAILED";
            status.message = "Device fault: " + m_driver->getLastError();
            break;
        default:
            status.status = "UNKNOWN";
            status.message = "Unknown status";
            break;
    }
    
    // 添加温度和转速信息
    try {
        auto reading = m_driver->readTemperatureAndSpeed();
        if (reading.success) {
            status.data["current_temperature"] = reading.temperature;
            status.data["current_speed"] = reading.speed;
        }
    } catch (const std::exception& e) {
        LOG(WARNING) << "Failed to read temperature and speed: " << e.what();
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::executeOperation(const json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }

    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        json data = request.contains("data") ? request["data"] : json();
        return handleStartOperation(data);
    } else if (action == "STOP") {
        json data = request.contains("data") ? request["data"] : json();
        return handleStopOperation(data);
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::handleStartOperation(const json& data) {
    if (!m_driver) {
        return createErrorTask("START", "Driver not initialized");
    }

    if (!m_driver->isConnected()) {
        return createErrorTask("START", "Device not connected");
    }



    try {
        int channel = data.contains("channel") ? data["channel"].get<int>() : 0;

        // 根据新接口规范，支持stirSpeed参数名
        // 默认参数：1000转、60°C、30分钟
        uint16_t stirSpeed = static_cast<uint16_t>(data.contains("stirSpeed") ? data["stirSpeed"].get<int>() :
                                                  (data.contains("speed") ? data["speed"].get<int>() : 1000));
        uint16_t temperature = static_cast<uint16_t>(data.contains("temperature") ? data["temperature"].get<int>() : 60);
        uint16_t timerMinutes = static_cast<uint16_t>(data.contains("timer_minutes") ? data["timer_minutes"].get<int>() : 30);

        LOG(INFO) << "Starting heating and stirring with smart heating: stirSpeed=" << stirSpeed
                  << " RPM, temperature=" << temperature << "°C, timer=" << timerMinutes << " min";

        // 1. 先下降温度传感器进入溶液
        bool sensorLowered = true;
        if (m_plcDriver) {
            LOG(INFO) << "Lowering temperature sensor for channel " << channel;
            if (!m_plcDriver->lowerTemperatureSensor(channel)) {
                LOG(WARNING) << "Failed to lower temperature sensor: " << m_plcDriver->getLastError();
                sensorLowered = false;
            } else {
                LOG(INFO) << "Temperature sensor lowered successfully";
            }
        }

        // 2. 使用智能快速升温接口
        if (m_driver->startStirringAndHeating(stirSpeed, temperature, timerMinutes)) {
            json responseData;
            responseData["channel"] = channel;
            responseData["heating"] = true;
            responseData["stirring"] = true;
            responseData["stirSpeed"] = stirSpeed;
            responseData["temperature"] = temperature;
            responseData["timer_minutes"] = timerMinutes;
            responseData["smart_heating"] = true;  // Indicates smart heating is used
            responseData["sensor_lowered"] = sensorLowered;  // Temperature sensor position

            auto task = createSuccessTask("START", responseData);
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Smart heating and stirring started successfully for channel " << channel;
            return task;
        } else {
            return createErrorTask("START", "Smart heating start failed: " + m_driver->getLastError());
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "Start exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::handleStopOperation(const json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("STOP", "Device not connected");
    }
    
    try {
        int channel = data.contains("channel") ? data["channel"].get<int>() : 0;
        int flushTimeout = data.contains("flushTimeout") ? data["flushTimeout"].get<int>() : 10;

        LOG(INFO) << "Stopping heating and stirring for channel " << channel
                  << " with flush timeout " << flushTimeout << " seconds";

        // 根据接口规范，STOP操作执行：停止搅拌 → 上升温度传感器 → 冲洗动作
        // 1. 使用智能停止接口一键停止所有功能
        if (!m_driver->stopAll()) {
            return createErrorTask("STOP", "Smart stop failed: " + m_driver->getLastError());
        }

        // 2. 上升温度传感器脱离溶液
        bool sensorRaised = true;
        if (m_plcDriver) {
            LOG(INFO) << "Raising temperature sensor for channel " << channel;
            if (!m_plcDriver->raiseTemperatureSensor(channel)) {
                sensorRaised = false;
                LOG(WARNING) << "Failed to raise temperature sensor: " << m_plcDriver->getLastError();
            } else {
                LOG(INFO) << "Temperature sensor raised successfully";
            }
        }

        // 3. 执行冲洗操作（如果有PLC驱动且flushTimeout > 0）
        bool flushSuccess = true;
        std::string flushMessage = "";

        if (m_plcDriver && flushTimeout > 0) {
            LOG(INFO) << "Executing temperature sensor flush for channel " << channel;
            if (!m_plcDriver->executeTemperatureSensorFlush(channel, flushTimeout)) {
                flushSuccess = false;
                flushMessage = "Flush failed: " + m_plcDriver->getLastError();
                LOG(WARNING) << flushMessage;
            } else {
                LOG(INFO) << "Temperature sensor flush started successfully";
            }
        }

        json responseData;
        responseData["channel"] = channel;
        responseData["heating"] = false;
        responseData["stirring"] = false;
        responseData["timer_cleared"] = true;
        responseData["smart_stop"] = true;
        responseData["sensor_raised"] = sensorRaised;  // Temperature sensor position
        responseData["flush_executed"] = (m_plcDriver && flushTimeout > 0);
        responseData["flush_success"] = flushSuccess;
        responseData["flushTimeout"] = flushTimeout;

        if (!flushMessage.empty()) {
            responseData["flush_message"] = flushMessage;
        }

        auto task = createSuccessTask("STOP", responseData);
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }

        LOG(INFO) << "Stop operation completed for channel " << channel
                  << ", sensor raised: " << (sensorRaised ? "success" : "failed")
                  << ", flush: " << (flushSuccess ? "success" : "warning");
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop exception: " + std::string(e.what()));
    }
}









int HeatingMagneticStirrerController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::createSuccessTask(const std::string& action, const json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();

    return task;
}

std::string HeatingMagneticStirrerController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}




