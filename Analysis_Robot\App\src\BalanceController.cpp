﻿#include "BalanceController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include "glog.h"

BalanceController::BalanceController(std::shared_ptr<AnalysisRobot::Balance::BalanceDriver> driver)
    : m_driver(driver)
    , m_nextTaskId(1000) {
    LOG(INFO) << "Balance controller created";
}

BalanceController::~BalanceController() {
    LOG(INFO) << "Balance controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus BalanceController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Balance Device";
    status.description = "Precision electronic balance";
    status.updateTime = getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "ERROR";
        status.message = "Driver not initialized";
        return status;
    }
    
    if (!m_driver->isConnected()) {
        status.status = "DISCONNECTED";
        status.message = "Device not connected";
        return status;
    }
    
    auto driverStatus = m_driver->getStatus();
    switch (driverStatus) {
        case AnalysisRobot::Balance::BalanceStatus::CONNECTED:
            status.status = "IDLE";
            status.message = "Device idle and available";
            break;
        case AnalysisRobot::Balance::BalanceStatus::READING:
            status.status = "BUSY";
            status.message = "Reading weight";
            break;
        case AnalysisRobot::Balance::BalanceStatus::FAULT:
            status.status = "FAILED";
            status.message = "Device fault: " + m_driver->getLastError();
            break;
        default:
            status.status = "UNKNOWN";
            status.message = "Unknown status";
            break;
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "WEIGH") {
        return handleWeighOperation(request.value("data", nlohmann::json()));
    } else if (action == "RESET") {
        return handleResetOperation();
    } else if (action == "TARE") {
        return handleTareOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::handleWeighOperation(const nlohmann::json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("WEIGH", "Device not connected");
    }
    
    try {
        // 读取重量
        auto reading = m_driver->readWeight();
        
        if (!reading.success) {
            return createErrorTask("WEIGH", "Weighing failed: " + reading.errorMsg);
        }
        
        // 创建响应数据
        nlohmann::json responseData;
        responseData["actualValue"] = reading.weight;
        
        auto task = createSuccessTask("WEIGH", responseData);
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Weight reading: " << reading.weight << "g";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("WEIGH", "Weighing exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::handleTareOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("TARE", "Device not connected");
    }
    
    try {
        if (m_driver->tare()) {
            auto task = createSuccessTask("TARE");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Tare operation completed";
            return task;
        } else {
            return createErrorTask("TARE", "Tare operation failed: " + m_driver->getLastError());
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("TARE", "Tare exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::handleResetOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("RESET", "Device not connected");
    }
    
    try {
        // 天平复位通常就是去皮操作
        if (m_driver->tare()) {
            auto task = createSuccessTask("RESET");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Reset operation completed";
            return task;
        } else {
            return createErrorTask("RESET", "Reset operation failed: " + m_driver->getLastError());
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("RESET", "Reset exception: " + std::string(e.what()));
    }
}

int BalanceController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo BalanceController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string BalanceController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


