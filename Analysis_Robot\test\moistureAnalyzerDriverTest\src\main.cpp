#include "MoistureAnalyzerDriver.h"
#include <iostream>
#include <iomanip>
#include <thread>
#include <chrono>

using namespace AnalysisRobot::Moisture;

void statusCallback(MoistureStatus status, const std::string& message)
{
    std::cout << "Status: ";
    switch (status)
    {
    case MoistureStatus::DISCONNECTED:
        std::cout << "DISCONNECTED";
        break;
    case MoistureStatus::CONNECTED:
        std::cout << "CONNECTED";
        break;
    case MoistureStatus::CALIBRATING:
        std::cout << "CALIBRATING";
        break;
    case MoistureStatus::HEATING:
        std::cout << "HEATING";
        break;
    case MoistureStatus::MEASURING:
        std::cout << "MEASURING";
        break;
    case MoistureStatus::FAULT:
        std::cout << "FAULT";
        break;
    }
    std::cout << " - " << message << std::endl;
}

int main()
{
    std::cout << "=== 水分测试仪驱动测试程序 ===" << std::endl;

    // 创建水分测试仪驱动实例
    MoistureAnalyzerDriver moistureAnalyzer;

    // 设置状态回调
    moistureAnalyzer.setStatusCallback(statusCallback);

    // 配置参数
    MoistureConfig config;
    config.serialPort = "COM9"; // 根据实际情况修改串口
    config.baudRate = 9600;
    config.parity = 'N';
    config.dataBits = 8;
    config.stopBits = 1;
    config.slaveId = 0x01; // 水分测试仪默认地址
    config.responseTimeout = 5000;
    config.targetTemperature = 180; // 默认目标温度105°C

    std::cout << "配置参数:" << std::endl;
    std::cout << "  串口: " << config.serialPort << std::endl;
    std::cout << "  波特率: " << config.baudRate << std::endl;
    std::cout << "  从机地址: 0x" << std::hex << config.slaveId << std::dec << std::endl;
    std::cout << "  目标温度: " << config.targetTemperature << "°C" << std::endl;
    std::cout << std::endl;

    // 初始化和连接
    std::cout << "正在初始化水分测试仪..." << std::endl;
    if (!moistureAnalyzer.initialize(config))
    {
        std::cout << "初始化失败: " << moistureAnalyzer.getLastError() << std::endl;
        return -1;
    }

    std::cout << "正在连接水分测试仪..." << std::endl;
    if (!moistureAnalyzer.connect())
    {
        std::cout << "连接失败: " << moistureAnalyzer.getLastError() << std::endl;
        return -1;
    }

    std::cout << "连接成功!" << std::endl;
    std::cout << std::endl;

    // 显示设备信息
    std::cout << "=== 设备信息 ===" << std::endl;
    std::cout << moistureAnalyzer.getDeviceInfo() << std::endl;

    // 交互式菜单
    int choice;
    while (true)
    {
        std::cout << "\n=== 水分测试仪操作菜单 ===" << std::endl;
        std::cout << "1. 读取净重" << std::endl;
        std::cout << "2. 读取水分" << std::endl;
        std::cout << "3. 读取过程状态" << std::endl;
        std::cout << "4. 读取称重稳定状态" << std::endl;
        std::cout << "5. 去皮操作" << std::endl;
        std::cout << "6. 内校操作" << std::endl;
        std::cout << "7. 开始加热" << std::endl;
        std::cout << "8. 停止加热" << std::endl;
        std::cout << "9. 返回称重模式" << std::endl;
        std::cout << "10. 开仓" << std::endl;
        std::cout << "11. 关仓" << std::endl;
        std::cout << "12. 设置目标温度" << std::endl;
        std::cout << "13. 配置站地址" << std::endl;
        std::cout << "14. 完整水分测试流程" << std::endl;
        std::cout << "15. 自动监控水分测试流程" << std::endl;
        std::cout << "16. 显示设备信息" << std::endl;
        std::cout << "0. 退出程序" << std::endl;
        std::cout << "请选择操作: ";

        std::cin >> choice;

        switch (choice)
        {
        case 1:
            {
                std::cout << "\n=== 读取净重 ===" << std::endl;
                WeightReading reading = moistureAnalyzer.readNetWeight();

                if (reading.success)
                {
                    std::cout << "  净重: " << std::fixed << std::setprecision(4)
                        << reading.weight << " g" << std::endl;
                    std::cout << "  内码值: 0x" << std::hex << std::uppercase
                        << reading.rawValue << std::dec << " (" << reading.rawValue << ")" << std::endl;
                }
                else
                {
                    std::cout << "  读取失败: " << reading.errorMsg << std::endl;
                }
                break;
            }

        case 2:
            {
                std::cout << "\n=== 读取水分 ===" << std::endl;
                MoistureReading reading = moistureAnalyzer.readMoisture();

                if (reading.success)
                {
                    std::cout << "  水分含量: " << std::fixed << std::setprecision(2)
                        << reading.moisture << " %" << std::endl;
                    std::cout << "  干重: " << std::fixed << std::setprecision(4)
                        << reading.dryWeight << " g" << std::endl;
                }
                else
                {
                    std::cout << "  读取失败: " << reading.errorMsg << std::endl;
                }
                break;
            }

        case 3:
            {
                std::cout << "\n=== 读取过程状态 ===" << std::endl;
                ProcessStatus status = moistureAnalyzer.readProcessStatus();

                std::cout << "  过程状态: ";
                switch (status)
                {
                case ProcessStatus::NORMAL:
                    std::cout << "正常";
                    break;
                case ProcessStatus::CALIBRATING:
                    std::cout << "内校校准中";
                    break;
                case ProcessStatus::CALIBRATION_FAULT:
                    std::cout << "内校故障";
                    break;
                case ProcessStatus::CALIBRATION_COMPLETE:
                    std::cout << "内校校准完毕";
                    break;
                case ProcessStatus::CALIBRATION_INVALID:
                    std::cout << "本次内校无效";
                    break;
                case ProcessStatus::CLEAR_PAN:
                    std::cout << "请清盘";
                    break;
                case ProcessStatus::REMOVING_WEIGHT:
                    std::cout << "取下砝码中(清盘中)";
                    break;
                }
                std::cout << std::endl;
                break;
            }

        case 4:
            {
                std::cout << "\n=== 读取称重稳定状态 ===" << std::endl;
                bool stable = moistureAnalyzer.readWeighingStability();
                std::cout << "  称重稳定状态: " << (stable ? "稳定" : "不稳定") << std::endl;
                break;
            }

        case 5:
            {
                std::cout << "\n=== 去皮操作 ===" << std::endl;
                std::cout << "正在执行去皮..." << std::endl;

                if (moistureAnalyzer.tare())
                {
                    std::cout << "去皮成功!" << std::endl;
                }
                else
                {
                    std::cout << "去皮失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 6:
            {
                std::cout << "\n=== 内校操作 ===" << std::endl;
                std::cout << "正在执行内校..." << std::endl;

                if (moistureAnalyzer.internalCalibration())
                {
                    std::cout << "内校启动成功!" << std::endl;
                    std::cout << "请等待内校完成，可通过读取过程状态查看进度" << std::endl;
                }
                else
                {
                    std::cout << "内校失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 7:
            {
                std::cout << "\n=== 开始加热 ===" << std::endl;
                std::cout << "正在启动加热..." << std::endl;

                if (moistureAnalyzer.startHeating())
                {
                    std::cout << "加热启动成功!" << std::endl;
                }
                else
                {
                    std::cout << "加热启动失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 8:
            {
                std::cout << "\n=== 停止加热 ===" << std::endl;
                std::cout << "正在停止加热..." << std::endl;

                if (moistureAnalyzer.stopHeating())
                {
                    std::cout << "加热停止成功!" << std::endl;
                }
                else
                {
                    std::cout << "加热停止失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 9:
            {
                std::cout << "\n=== 返回称重模式 ===" << std::endl;
                std::cout << "正在返回称重模式..." << std::endl;

                if (moistureAnalyzer.returnToWeighing())
                {
                    std::cout << "返回称重模式成功!" << std::endl;
                }
                else
                {
                    std::cout << "返回称重模式失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 10:
            {
                std::cout << "\n=== 开仓 ===" << std::endl;
                std::cout << "正在开仓..." << std::endl;

                if (moistureAnalyzer.openChamber())
                {
                    std::cout << "开仓成功!" << std::endl;
                }
                else
                {
                    std::cout << "开仓失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 11:
            {
                std::cout << "\n=== 关仓 ===" << std::endl;
                std::cout << "正在关仓..." << std::endl;

                if (moistureAnalyzer.closeChamber())
                {
                    std::cout << "关仓成功!" << std::endl;
                }
                else
                {
                    std::cout << "关仓失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 12:
            {
                std::cout << "\n=== 设置目标温度 ===" << std::endl;
                int temperature;
                std::cout << "请输入目标温度 (°C): ";
                std::cin >> temperature;

                if (moistureAnalyzer.setTargetTemperature(temperature))
                {
                    std::cout << "目标温度设置成功: " << temperature << "°C" << std::endl;
                }
                else
                {
                    std::cout << "目标温度设置失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                break;
            }

        case 13:
            {
                std::cout << "\n=== 配置站地址 ===" << std::endl;
                int address;
                std::cout << "请输入新的站地址 (1-255): ";
                std::cin >> address;

                if (address >= 1 && address <= 255)
                {
                    if (moistureAnalyzer.configureStationAddress(address))
                    {
                        std::cout << "站地址配置成功: " << address << std::endl;
                        std::cout << "注意: 地址已保存到EEPROM，重启后生效" << std::endl;
                    }
                    else
                    {
                        std::cout << "站地址配置失败: " << moistureAnalyzer.getLastError() << std::endl;
                    }
                }
                else
                {
                    std::cout << "无效的站地址，请输入1-255之间的值" << std::endl;
                }
                break;
            }

        case 14:
            {
                std::cout << "\n=== 完整水分测试流程 ===" << std::endl;
                std::cout << "开始执行完整的水分测试流程..." << std::endl;


                // 1. 去皮
                std::cout << "3. 去皮..." << std::endl;
                if (!moistureAnalyzer.tare())
                {
                    std::cout << "去皮失败: " << moistureAnalyzer.getLastError() << std::endl;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));

                // 2. 开仓
                std::cout << "1. 开仓..." << std::endl;
                if (!moistureAnalyzer.openChamber())
                {
                    std::cout << "开仓失败: " << moistureAnalyzer.getLastError() << std::endl;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));

                std::cout << "请放入样品，然后按回车键继续..." << std::endl;
                std::cin.ignore();
                std::cin.get();

                // 3. 关仓
                std::cout << "2. 关仓..." << std::endl;
                if (!moistureAnalyzer.closeChamber())
                {
                    std::cout << "关仓失败: " << moistureAnalyzer.getLastError() << std::endl;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::seconds(10));


                // 4. 读取初始重量
                std::cout << "4. 读取初始重量..." << std::endl;
                WeightReading initialWeight = moistureAnalyzer.readNetWeight();
                if (initialWeight.success)
                {
                    std::cout << "   初始重量: " << std::fixed << std::setprecision(4)
                        << initialWeight.weight << " g" << std::endl;
                    // 设置初始重量到驱动中，用于后续水分计算
                    moistureAnalyzer.setInitialWeight(initialWeight.weight);
                }
                else
                {
                    std::cout << "读取初始重量失败: " << initialWeight.errorMsg << std::endl;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));

                if (moistureAnalyzer.setTargetTemperature(230))
                {
                    std::cout << "目标温度设置成功: " << 230 << "°C" << std::endl;
                }
                else
                {
                    std::cout << "目标温度设置失败: " << moistureAnalyzer.getLastError() << std::endl;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));

                // 5. 开始加热
                std::cout << "5. 开始加热..." << std::endl;
                if (!moistureAnalyzer.startHeating())
                {
                    std::cout << "开始加热失败: " << moistureAnalyzer.getLastError() << std::endl;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));

                // 等待设备稳定
                std::cout << "   等待设备稳定..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(10));

                // 6. 自动监控加热过程
                std::cout << "6. 自动监控加热过程 (连续30次检测结果不变时自动结束)..." << std::endl;

                // 定义进度回调函数
                auto progressCallback = [](double moisture, int stableCount, int elapsedSeconds) {
                    std::cout << "   时间: " << elapsedSeconds << "s, "
                              << "水分: " << std::fixed << std::setprecision(2) << moisture << "%, "
                              << "稳定计数: " << stableCount << "/60" << std::endl;
                };

                // 使用自动监控方法（30次不变，容差0.01%，间隔2秒，最大10分钟）
                MoistureReading autoResult = moistureAnalyzer.autoMonitorMoisture(30, 0.01, 2, 30, progressCallback);

                if (autoResult.success)
                {
                    std::cout << "自动监控完成！" << std::endl;
                    std::cout << "   最终水分: " << std::fixed << std::setprecision(2) << autoResult.moisture << "%" << std::endl;
                    std::cout << "   干燥后重量: " << std::fixed << std::setprecision(4) << autoResult.dryWeight << "g" << std::endl;
                    std::cout << "   初始重量: " << std::fixed << std::setprecision(4) << autoResult.initialWeight << "g" << std::endl;
                }
                else
                {
                    std::cout << "自动监控失败: " << autoResult.errorMsg << std::endl;
                }

                // 7. 停止加热
                std::cout << "7. 停止加热..." << std::endl;
                if (moistureAnalyzer.stopHeating())
                {
                    std::cout << "   停止加热成功" << std::endl;
                }
                else
                {
                    std::cout << "   停止加热失败: " << moistureAnalyzer.getLastError() << std::endl;
                }


                std::this_thread::sleep_for(std::chrono::seconds(1));

                // 9. 读取返回称重模式
                std::cout << "正在返回称重模式..." << std::endl;

                if (moistureAnalyzer.returnToWeighing())
                {
                    std::cout << "返回称重模式成功!" << std::endl;
                }
                else
                {
                    std::cout << "返回称重模式失败: " << moistureAnalyzer.getLastError() << std::endl;
                }


                std::cout << "水分测试流程完成!" << std::endl;
                break;
            }

        case 15:
            {
                std::cout << "\n=== 自动监控水分测试流程 ===" << std::endl;
                std::cout << "开始执行自动监控水分测试流程..." << std::endl;

                // 1. 去皮
                std::cout << "1. 去皮..." << std::endl;
                if (moistureAnalyzer.tare())
                {
                    std::cout << "   去皮成功" << std::endl;
                }
                else
                {
                    std::cout << "   去皮失败: " << moistureAnalyzer.getLastError() << std::endl;
                    break;
                }

                // 2. 等待放样
                std::cout << "2. 请放入样品，然后按回车键继续..." << std::endl;
                std::cin.ignore(); // 清除之前的输入缓冲
                std::cin.get();

                // 3. 读取初始重量
                std::cout << "3. 读取初始重量..." << std::endl;
                WeightReading initialWeight = moistureAnalyzer.readNetWeight();
                if (initialWeight.success)
                {
                    std::cout << "   初始重量: " << std::fixed << std::setprecision(4) << initialWeight.weight << "g" << std::endl;
                    moistureAnalyzer.setInitialWeight(initialWeight.weight);
                }
                else
                {
                    std::cout << "   读取初始重量失败: " << initialWeight.errorMsg << std::endl;
                    break;
                }

                // 4. 开始加热
                std::cout << "4. 开始加热..." << std::endl;
                if (moistureAnalyzer.startHeating())
                {
                    std::cout << "   加热开始" << std::endl;
                }
                else
                {
                    std::cout << "   开始加热失败: " << moistureAnalyzer.getLastError() << std::endl;
                    break;
                }

                // 等待设备稳定
                std::cout << "   等待设备稳定..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(10));

                // 5. 自动监控水分测定过程
                std::cout << "5. 自动监控水分测定过程..." << std::endl;
                std::cout << "   (连续30次检测结果不变时自动结束)" << std::endl;

                // 定义进度回调函数
                auto progressCallback = [](double moisture, int stableCount, int elapsedSeconds) {
                    std::cout << "   时间: " << elapsedSeconds << "s, "
                              << "水分: " << std::fixed << std::setprecision(2) << moisture << "%, "
                              << "稳定计数: " << stableCount << "/60" << std::endl;
                };

                // 调用自动监控方法（30次不变，容差0.01%，间隔5秒，最大10分钟）
                MoistureReading autoResult = moistureAnalyzer.autoMonitorMoisture(30, 0.01, 5, 10, progressCallback);

                if (autoResult.success)
                {
                    std::cout << "   自动监控完成！" << std::endl;
                    std::cout << "   最终水分: " << std::fixed << std::setprecision(2) << autoResult.moisture << "%" << std::endl;
                    std::cout << "   干燥后重量: " << std::fixed << std::setprecision(4) << autoResult.dryWeight << "g" << std::endl;
                    std::cout << "   初始重量: " << std::fixed << std::setprecision(4) << autoResult.initialWeight << "g" << std::endl;
                }
                else
                {
                    std::cout << "   自动监控失败: " << autoResult.errorMsg << std::endl;
                }

                // 6. 停止加热
                std::cout << "6. 停止加热..." << std::endl;
                if (moistureAnalyzer.stopHeating())
                {
                    std::cout << "   停止加热成功" << std::endl;
                }
                else
                {
                    std::cout << "   停止加热失败: " << moistureAnalyzer.getLastError() << std::endl;
                }

                std::cout << "自动监控水分测试流程完成!" << std::endl;
                break;
            }

        case 16:
            {
                std::cout << "\n=== 设备信息 ===" << std::endl;
                std::cout << moistureAnalyzer.getDeviceInfo() << std::endl;
                break;
            }

        case 0:
            {
                std::cout << "\n正在断开连接..." << std::endl;
                moistureAnalyzer.disconnect();
                std::cout << "测试完成!" << std::endl;
                return 0;
            }

        default:
            {
                std::cout << "无效选择，请重新输入" << std::endl;
                break;
            }
        }
    }

    return 0;
}
