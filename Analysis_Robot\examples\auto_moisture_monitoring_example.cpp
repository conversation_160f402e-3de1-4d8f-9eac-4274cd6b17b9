/**
 * @file auto_moisture_monitoring_example.cpp
 * @brief 自动监控水分测定示例
 * 
 * 本示例展示如何使用 MoistureAnalyzerDriver 的自动监控功能，
 * 当连续30次检测结果维持不变时自动结束水分含量测定。
 */

#include "MoistureAnalyzerDriver.h"
#include <iostream>
#include <iomanip>

using namespace AnalysisRobot::Moisture;

void statusCallback(MoistureStatus status, const std::string& message)
{
    std::cout << "设备状态: ";
    switch (status)
    {
    case MoistureStatus::DISCONNECTED:
        std::cout << "未连接";
        break;
    case MoistureStatus::CONNECTED:
        std::cout << "已连接";
        break;
    case MoistureStatus::CALIBRATING:
        std::cout << "校准中";
        break;
    case MoistureStatus::HEATING:
        std::cout << "加热中";
        break;
    case MoistureStatus::MEASURING:
        std::cout << "测量中";
        break;
    case MoistureStatus::FAULT:
        std::cout << "故障";
        break;
    }
    std::cout << " - " << message << std::endl;
}

int main()
{
    std::cout << "=== 自动监控水分测定示例 ===" << std::endl;
    
    // 创建水分测定仪驱动实例
    MoistureAnalyzerDriver moistureAnalyzer;
    
    // 设置状态回调
    moistureAnalyzer.setStatusCallback(statusCallback);
    
    // 配置参数
    MoistureConfig config;
    config.serialPort = "COM9";         // 根据实际情况修改串口
    config.baudRate = 9600;
    config.parity = 'N';
    config.dataBits = 8;
    config.stopBits = 1;
    config.slaveId = 0x01;              // 水分测定仪默认地址
    config.responseTimeout = 5000;
    config.targetTemperature = 105;     // 目标温度105°C
    
    std::cout << "配置参数:" << std::endl;
    std::cout << "  串口: " << config.serialPort << std::endl;
    std::cout << "  波特率: " << config.baudRate << std::endl;
    std::cout << "  从机地址: 0x" << std::hex << config.slaveId << std::dec << std::endl;
    std::cout << "  目标温度: " << config.targetTemperature << "°C" << std::endl;
    std::cout << std::endl;
    
    // 初始化和连接
    std::cout << "正在初始化水分测定仪..." << std::endl;
    if (!moistureAnalyzer.initialize(config))
    {
        std::cout << "初始化失败: " << moistureAnalyzer.getLastError() << std::endl;
        return -1;
    }
    
    std::cout << "正在连接水分测定仪..." << std::endl;
    if (!moistureAnalyzer.connect())
    {
        std::cout << "连接失败: " << moistureAnalyzer.getLastError() << std::endl;
        return -1;
    }
    
    std::cout << "连接成功!" << std::endl;
    std::cout << std::endl;
    
    try {
        // 1. 去皮
        std::cout << "1. 执行去皮操作..." << std::endl;
        if (!moistureAnalyzer.tare())
        {
            std::cout << "   去皮失败: " << moistureAnalyzer.getLastError() << std::endl;
            return -1;
        }
        std::cout << "   去皮成功" << std::endl;
        
        // 2. 等待放样
        std::cout << "2. 请放入样品，然后按回车键继续..." << std::endl;
        std::cin.get();
        
        // 3. 读取初始重量
        std::cout << "3. 读取初始重量..." << std::endl;
        WeightReading initialWeight = moistureAnalyzer.readNetWeight();
        if (!initialWeight.success)
        {
            std::cout << "   读取初始重量失败: " << initialWeight.errorMsg << std::endl;
            return -1;
        }
        
        std::cout << "   初始重量: " << std::fixed << std::setprecision(4) 
                  << initialWeight.weight << "g" << std::endl;
        moistureAnalyzer.setInitialWeight(initialWeight.weight);
        
        // 4. 开始加热
        std::cout << "4. 开始加热..." << std::endl;
        if (!moistureAnalyzer.startHeating())
        {
            std::cout << "   开始加热失败: " << moistureAnalyzer.getLastError() << std::endl;
            return -1;
        }
        std::cout << "   加热开始" << std::endl;
        
        // 5. 自动监控水分测定过程
        std::cout << "5. 开始自动监控水分测定过程..." << std::endl;
        std::cout << "   参数设置:" << std::endl;
        std::cout << "   - 连续不变次数: 30次" << std::endl;
        std::cout << "   - 变化容差: 0.01%" << std::endl;
        std::cout << "   - 检测间隔: 5秒" << std::endl;
        std::cout << "   - 最大超时: 10分钟" << std::endl;
        std::cout << std::endl;
        
        // 定义进度回调函数
        auto progressCallback = [](double moisture, int stableCount, int elapsedSeconds) {
            std::cout << "   [" << std::setw(3) << elapsedSeconds << "s] "
                      << "水分: " << std::fixed << std::setprecision(2) << std::setw(6) << moisture << "%, "
                      << "稳定计数: " << std::setw(2) << stableCount << "/30";
            
            if (stableCount > 0) {
                std::cout << " [稳定中...]";
            }
            std::cout << std::endl;
        };
        
        // 调用自动监控方法
        MoistureReading result = moistureAnalyzer.autoMonitorMoisture(
            30,                 // 最大连续不变次数
            0.01,               // 变化容差 (%)
            5,                  // 检测间隔 (秒)
            10,                 // 最大超时 (分钟)
            progressCallback    // 进度回调函数
        );
        
        // 6. 停止加热
        std::cout << "6. 停止加热..." << std::endl;
        if (!moistureAnalyzer.stopHeating())
        {
            std::cout << "   停止加热失败: " << moistureAnalyzer.getLastError() << std::endl;
        }
        else
        {
            std::cout << "   停止加热成功" << std::endl;
        }
        
        // 7. 显示结果
        std::cout << std::endl;
        std::cout << "=== 测定结果 ===" << std::endl;
        if (result.success)
        {
            std::cout << "✓ 自动监控完成！" << std::endl;
            std::cout << "  最终水分含量: " << std::fixed << std::setprecision(2) 
                      << result.moisture << "%" << std::endl;
            std::cout << "  干燥后重量: " << std::fixed << std::setprecision(4) 
                      << result.dryWeight << "g" << std::endl;
            std::cout << "  初始重量: " << std::fixed << std::setprecision(4) 
                      << result.initialWeight << "g" << std::endl;
            std::cout << "  失重: " << std::fixed << std::setprecision(4) 
                      << (result.initialWeight - result.dryWeight) << "g" << std::endl;
        }
        else
        {
            std::cout << "✗ 自动监控失败: " << result.errorMsg << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
    }
    
    // 断开连接
    std::cout << std::endl;
    std::cout << "正在断开连接..." << std::endl;
    moistureAnalyzer.disconnect();
    std::cout << "测试完成!" << std::endl;
    
    return 0;
}
